{% extends "base.html" %}

{% block title %}Help desk - Footprints{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/event-detail-bundle.css') }}">
<style>
    .journey-detail-card {
        background: #fff;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        border: 1px solid #f1f3f4;
    }

    .section-header {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 1px solid #f8f9fa;
        flex-shrink: 0;
    }

    .section-header h1,
    .section-header h2 {
        font-size: 18px;
        font-weight: 700;
        color: #212529;
        margin: 0;
    }

    .event-title {
        font-size: 24px;
        font-weight: 700;
        color: #212529;
        margin: 0;
        line-height: 1.3;
    }

    .author-info,
    .event-meta {
        display: flex;
        gap: 8px;
    }

    .author-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        overflow: hidden;
        border: 2px solid #e9ecef;
    }

    .author-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .author-details {
        display: flex;
        flex-direction: column;
        gap: 1px;
    }

    .author-name {
        font-weight: 600;
        color: #212529;
        font-size: 13px;
    }

    .update-time {
        color: #6c757d;
        font-size: 11px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .badge {
        font-size: 11px;
        font-weight: 600;
        border-radius: 12px;
        padding: 3px 10px;
    }

    .content-section {
        display: flex;
        flex-direction: column;
        min-height: 0;
        margin-bottom: 16px;
    }

    .section-content {
        color: #495057;
        line-height: 1.5;
        font-size: 14px;
        flex: 1;
        overflow: hidden;
    }

    .comments-section {
        display: flex;
        flex-direction: column;
        height: 100%;
    }

    .comments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        padding-bottom: 12px;
        border-bottom: 1px solid #e9ecef;
        flex-shrink: 0;
    }

    .comments-header h2 {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 18px;
        font-weight: 600;
        color: #212529;
        margin: 0;
    }

    .comment-count {
        background: #f8f9fa;
        color: #495057;
        padding: 3px 10px;
        border-radius: 14px;
        font-size: 11px;
        font-weight: 600;
    }

    .comments-list {
        max-height: calc(100vh - 500px);
        overflow-y: auto;
    }

    .comment-item {
        display: flex;
        gap: 12px;
        padding: 16px 0;
        border-bottom: 1px solid #f8f9fa;
    }

    .comment-item:last-child {
        border-bottom: none;
    }

    .comment-avatar {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        overflow: hidden;
        flex-shrink: 0;
    }

    .comment-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .comment-content {
        flex: 1;
    }

    .comment-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;
    }

    .comment-author {
        font-weight: 600;
        color: #212529;
        font-size: 14px;
    }

    .comment-time {
        color: #6c757d;
        font-size: 12px;
    }

    .comment-text {
        color: #495057;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 8px;
        word-break: break-word;
        overflow-wrap: break-word;
    }

    .comment-form-container {
        flex-shrink: 0;
        margin-top: 0;
    }

    .comment-form {
        display: flex;
        gap: 12px;
        align-items: center;
    }

    .comment-input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #dee2e6;
        border-radius: 24px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.2s;
        min-height: 40px;
    }

    .comment-input:focus {
        border-color: #007bff;
    }

    .submit-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 50%;
        font-size: 16px;
        transition: background 0.2s;
    }

    .submit-btn:hover {
        background: #0056b3;
    }

    .empty-state {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
        text-align: center;
        min-height: 200px;
    }

    .empty-state-icon {
        margin-bottom: 16px;
    }

    .empty-state-icon i {
        font-size: 48px;
        color: #dee2e6;
        opacity: 0.8;
    }

    .empty-state h4 {
        font-size: 18px;
        font-weight: 600;
        color: #6c757d;
        margin: 0 0 8px 0;
    }

    .empty-state p {
        font-size: 14px;
        color: #adb5bd;
        margin: 0;
        line-height: 1.5;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Back button -->
    <a href="javascript:void(0)" onclick="smartBack()"
        class="back-button d-inline-flex align-items-center text-decoration-none text-dark mb-3">
        <i class="bi bi-arrow-left me-2"></i>
        <span id="backButtonText">Back</span>
    </a>

    <div class="row g-4" id="ticketContainer">
        <div class="col-md-5 ticket-detail-panel">
            <div class="card shadow-sm border-0 rounded-3 h-100"
                style="max-height: calc(100vh - 400px); overflow-y: auto; height: 100%; min-height: calc(100vh - 500px);">
                <div class="card-body">
                    <div class="d-flex align-items-center gap-2 mb-2">
                        <div class="journey-breadcrumb mb-2">
                            <i class="bi bi-journal-bookmark"></i>
                            <span>Ticket Details</span>
                        </div>
                        <div class="ms-auto">
                            <div class="dropdown" {% if can_manage_content() and ticket.status not in
                                ['resolved', 'approved' , 'rejected' ] %}{% else %}style='display: none;' {% endif %}>
                                <button class="menu-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="bi bi-three-dots-vertical"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow-sm">
                                    {% if ticket.user_id != g.current_user['id'] and ticket.assigned_to != session.user_id %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="take"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-plus-fill me-2 text-success"></i>{% if ticket.assigned_to %}Take Over Ticket{% else %}Take Ticket{% endif %}
                                        </a>
                                    </li>
                                    <li>
                                        <hr class="dropdown-divider">
                                    </li>
                                    {% endif %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="assign"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-plus me-2 text-dark"></i>{% if ticket.assigned_to
                                            %}Reassign Ticket{% else %}Assign Ticket{% endif %}
                                        </a>
                                    </li>
                                    {% if ticket.assigned_to and ticket.assigned_to == session.user_id and
                                    ticket.request_type != 'appeal' %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="change"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-pencil-square me-2 text-dark"></i>Change Status
                                        </a>
                                    </li>
                                    {% endif %}
                                    {% if ticket.assigned_to and ticket.assigned_to == session.user_id %}
                                    <li>
                                        <a class="dropdown-item modal-trigger" href="#" data-action="drop"
                                            data-ticket-id="{{ ticket.id }}">
                                            <i class="bi bi-person-dash-fill me-2"></i>Drop Ticket
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h1 class="event-title mb-1">{{ ticket.subject }}</h1>
                    <div class="content-section meta-section">
                        <div class="section-header" style="border-bottom: none; margin-bottom: 8px;">
                            {% include 'helpdesk/components/status_badge.html' %}
                            {% include 'helpdesk/components/assigned_badge.html' %}
                        </div>
                        <div class="section-content">
                            <div class="event-meta">
                                <div class="author-info">
                                    <div class="author-avatar">
                                        <img src="{% if ticket.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + ticket.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                                            alt="User Avatar"
                                            onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                                    </div>
                                    <div class="author-details">
                                        <span class="author-name">{{ ticket.username }}</span>
                                    </div>
                                </div>
                                <div class="update-time"><i class="bi bi-clock"></i>Created on {{
                                    ticket.created_at|datetime }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="content-section description-section">
                        <div class="section-header">
                            <h3>Description</h3>
                        </div>
                        <div class="section-content"><span style="font-size: 14px; color: #495057;">{{
                                ticket.description
                                }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-7">
            <div class="card shadow-sm border-0 rounded-3"
                style="max-height: calc(100vh - 400px); overflow-y: auto; height: 100%;">
                <div class="card-body comments-section">
                    <div class="comments-header">
                        <h2><i class="bi bi-chat-dots"></i>Comments</h2>
                        <span class="comment-count">{{ ticket.replies|length }}</span>
                    </div>
                    <div class="comments-list">
                        {% if ticket.replies %}
                        {% for reply in ticket.replies %}
                        <div class="comment-item">
                            <div class="comment-avatar">
                                <img src="{% if reply.profile_image %}{{ url_for('static', filename='uploads/profile_images/' + reply.profile_image) }}{% else %}{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}{% endif %}"
                                    alt="User Avatar"
                                    onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                            </div>
                            <div class="comment-content">
                                <div class="comment-header">
                                    <span class="comment-author">{{reply.username}}</span>
                                    <span class="comment-time"><i class="bi bi-clock me-1"></i>{{
                                        reply.created_at|datetime }}</span>
                                </div>
                                <div class="comment-text">{{reply.content}}</div>
                            </div>
                        </div>
                        {% endfor %}
                        {% else %}
                        <div class="empty-state text-center w-100">
                            <div class="empty-state-icon">
                                <i class="bi bi-chat-left-dots"></i>
                            </div>
                            <h4>No comments yet</h4>
                            <p>Be the first to share your thoughts about this ticket!</p>
                        </div>
                        {% endif %}
                    </div>
                    {% if ticket.status in ['resolved', 'approved', 'rejected'] or (session.user_id != ticket.user_id and session.user_id != ticket.assigned_to) %}
                    <div class="p-4 rounded-3 mt-3 mb-0 bg-light">
                        <div class="d-flex align-items-start">
                            <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                            <div>
                                {% if ticket.status in ['resolved', 'approved', 'rejected'] %}
                                <h6 class="fw-bold mb-1">Comments Disabled</h6>
                                <p class="mb-0 small">This ticket has been {{ ticket.status }} and no longer accepts new
                                    comments.</p>
                                {% elif session.user_id != ticket.user_id and session.user_id != ticket.assigned_to %}
                                <h6 class="fw-bold mb-1">Comments Restricted</h6>
                                <p class="mb-0 small">Only the ticket creator and assigned staff can add comments to
                                    this ticket.</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% else %}
                    <div class="comment-form-container mt-4">
                        <form action="{{url_for('helpdesk.add_reply')}}" id="post_comment" method="post"
                            class="comment-form">
                            <input type="hidden" name="ticket_id" value="{{ticket.id}}">
                            <input type="hidden" name="user_id" value="{{session['user_id']}}">
                            <!-- Preserve back parameter for navigation -->
                            {% if request.args.get('back') %}
                            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
                            {% endif %}
                            <input type="text" class="comment-input" id="content" placeholder="Your Comment"
                                name="content" required>
                            <button type="submit" class="submit-btn"><i class="bi bi-send"></i></button>
                        </form>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- HERE -->
    <!-- Show admin response for appeals -->
    <div class="content-section status-section mt-4">
        <div class="section-header">
            <h3>Status & Appeal</h3>
        </div>
        <div class="section-content">
            {% include 'helpdesk/components/status_body.html' %}
        </div>
    </div>
    <div id="assignTicketFormTemplate" style="display:none;">
        {% include 'helpdesk/components/assign_form.html' %}
    </div>
    <div id="changeStatusFormTemplate" style="display:none;">
        <form id="changeStatusForm" method="POST" action="{{ url_for('helpdesk.change_ticket_status') }}">
            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}

            <!-- Current Status Display -->
            <div class="mb-3">
                <label class="form-label fw-medium">Current Status</label>
                <div class="current-status-display">
                    {% include 'helpdesk/components/status_badge.html' %}
                </div>
            </div>

            <!-- New Status Selection -->
            <div class="mb-3">
                <label for="status" class="form-label fw-medium">Change Status To</label>
                <select class="form-control status-change-select" name="status" id="statusSelect">
                    {% if ticket.status == 'new' %}
                    <!-- From 'new': can go to 'open' or 'stalled' -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Start working on the ticket
                    </option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Waiting for information or resources
                    </option>
                    {% elif ticket.status == 'open' %}
                    <!-- From 'open': can go to 'stalled' or 'resolved' -->
                    <option value="">-- Select New Status --</option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Waiting for information or resources
                    </option>
                    <option value="resolved">
                        <i class="bi bi-check2-circle"></i> Resolved - Issue has been fixed
                    </option>
                    {% elif ticket.status == 'stalled' %}
                    <!-- From 'stalled': can go back to 'open' or forward to 'resolved' -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Resume working on the ticket
                    </option>
                    <option value="resolved">
                        <i class="bi bi-check2-circle"></i> Resolved - Issue has been fixed
                    </option>
                    {% elif ticket.status == 'resolved' %}
                    <!-- From 'resolved': can reopen if needed -->
                    <option value="">-- Select New Status --</option>
                    <option value="open">
                        <i class="bi bi-unlock"></i> Open - Reopen the ticket
                    </option>
                    <option value="stalled">
                        <i class="bi bi-pause"></i> Stalled - Needs additional work
                    </option>
                    {% endif %}
                </select>

                <!-- Status Change Guidelines -->
                <div class="form-text mt-2">
                    {% if ticket.status == 'new' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Open</strong> to start working on this ticket, or <strong>Stalled</strong> if you
                        need more information.
                    </small>
                    {% elif ticket.status == 'open' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Resolved</strong> when the issue is fixed, or <strong>Stalled</strong> if you're
                        waiting for something.
                    </small>
                    {% elif ticket.status == 'stalled' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        Choose <strong>Open</strong> to resume work, or <strong>Resolved</strong> if the issue is now
                        fixed.
                    </small>
                    {% elif ticket.status == 'resolved' %}
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        You can reopen this ticket if additional work is needed.
                    </small>
                    {% endif %}
                </div>
            </div>

            <!-- Status Change Confirmation -->
            <div id="statusChangePreview" class="alert status-preview rounded-3 mb-3" style="display: none;">
                <div class="d-flex align-items-center">
                    <i class="bi bi-arrow-right-circle fs-5 me-3 text-primary"></i>
                    <div>
                        <small class="fw-medium text-dark">Status will change from:</small><br>
                        <span class="badge status-badge bg-secondary me-2" id="currentStatusBadge">
                            {% if ticket.status == 'new' %}<i class="bi bi-star me-1"></i>{{ ticket.status|title }}{%
                            elif ticket.status == 'open' %}<i class="bi bi-unlock me-1"></i>{{ ticket.status|title }}{%
                            elif ticket.status == 'stalled' %}<i class="bi bi-pause me-1"></i>{{ ticket.status|title
                            }}{% elif ticket.status == 'resolved' %}<i class="bi bi-check2-circle me-1"></i>{{
                            ticket.status|title }}{% else %}{{ ticket.status|title }}{% endif %}
                        </span>
                        <i class="bi bi-arrow-right mx-1 status-transition-arrow"></i>
                        <span class="badge status-badge bg-primary" id="newStatusBadge"></span>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div id="takeTicketFormTemplate" style="display:none;">
        {% include 'helpdesk/components/take_form.html' %}
    </div>

    <div id="dropTicketFormTemplate" style="display:none;">
        {% include 'helpdesk/components/drop_form.html' %}
    </div>

    <!-- Appeal Processing Modal Templates - Only for appeal tickets assigned to current user -->
    {% if ticket.request_type == 'appeal' and ticket.appeal_type and ticket.status in ['new', 'open'] and ticket.assigned_to == session.user_id %}
    <div id="approveAppealFormTemplate" style="display:none;">
        <form id="approveAppealForm" method="POST"
            action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
            <input type="hidden" name="action" value="approve">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="alert alert-success rounded-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="bi bi-check-circle fs-5 me-3 mt-1"></i>
                    <div>
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        This will unhide the journey and make it visible to other users again.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        This will unblock the user and allow them to share journeys again.
                        {% elif ticket.appeal_type == 'ban' %}
                        This will unban the user and allow them to log in again.
                        {% else %}
                        This will approve the appeal and take the appropriate action.
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="approve_response" class="form-label">Response to User (Optional)</label>
                <textarea class="form-control" id="approve_response" name="response" rows="3"></textarea>
                <div class="form-text">This message will be sent to the user as a notification.</div>
            </div>
        </form>
    </div>

    <div id="rejectAppealFormTemplate" style="display:none;">
        <form id="rejectAppealForm" method="POST"
            action="{{ url_for('helpdesk.process_appeal', appeal_id=ticket.id) }}">
            <input type="hidden" name="action" value="reject">
            <!-- Preserve back parameter for navigation -->
            {% if request.args.get('back') %}
            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
            {% endif %}
            <div class="alert alert-danger rounded-3 mb-3">
                <div class="d-flex align-items-center justify-content-center">
                    <i class="bi bi-x-circle fs-5 me-3"></i>
                    <div>
                        {% if ticket.appeal_type == 'hidden_journey' %}
                        The journey will remain hidden. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'sharing_block' %}
                        The user will remain blocked from sharing. Please provide a clear explanation.
                        {% elif ticket.appeal_type == 'ban' %}
                        The user will remain banned from the system. Please provide a clear explanation.
                        {% else %}
                        The appeal will be rejected. Please provide a clear explanation.
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="mb-3">
                <label for="reject_response" class="form-label">Reason for Rejection <span
                        class="text-danger">*</span></label>
                <textarea class="form-control" id="reject_response" name="response" rows="4" required></textarea>
                <div class="form-text">This explanation will be sent to the user. Be specific and constructive.</div>
            </div>
        </form>
    </div>
    {% endif %}
</div>
<script>
    // Global variables for role checking
    const canManageContent = {% if can_manage_content() %}true{% else %}false{% endif %};
    const isLoggedIn = {% if g.current_user %}true{% else %}false{% endif %};

    // Appeal processing URL for JavaScript use (only for appeal tickets assigned to current user)
    {% if ticket.request_type == 'appeal' and ticket.appeal_type and ticket.status in ['new', 'open'] and ticket.assigned_to == session.user_id %}
    const appealProcessUrl = '{{ url_for("helpdesk.process_appeal", appeal_id=ticket.id) }}';
    {% else %}
    const appealProcessUrl = null;
    {% endif %}

    // Simple and reliable back button function for helpdesk detail page
    function smartBack() {
        // Priority 1: Check for explicit back URL parameter (highest priority)
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        if (backUrl) {
            try {
                const decodedUrl = decodeURIComponent(backUrl);
                // Only allow internal URLs for security
                if (decodedUrl.startsWith('/') || decodedUrl.startsWith(window.location.origin)) {
                    window.location.href = decodedUrl;
                    return;
                }
            } catch (e) {
                console.error('Invalid back URL:', e);
            }
        }

        // Priority 2: Check stored source from session storage (where user came from)
        const storedSource = sessionStorage.getItem('ticketDetailOriginalSource');
        if (storedSource) {
            // Direct navigation to stored source - prioritize where user came from
            if (storedSource.includes('/notifications/all')) {
                window.location.href = '{{ url_for("main.view_all_notifications") }}';
                return;
            } else if (storedSource.includes('/notifications')) {
                window.location.href = '{{ url_for("main.view_all_notifications") }}';
                return;
            } else if (storedSource.includes('/helpdesk/manage')) {
                window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
                return;
            } else if (storedSource.includes('/helpdesk') && !storedSource.includes('/helpdesk/ticket')) {
                window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                return;
            }
        }

        // Priority 3: Check document referrer as fallback
        const referrer = document.referrer;
        if (referrer) {
            if (referrer.includes('/notifications')) {
                window.location.href = '{{ url_for("main.view_all_notifications") }}';
                return;
            } else if (referrer.includes('/helpdesk/manage')) {
                window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
                return;
            } else if (referrer.includes('/helpdesk') && !referrer.includes('/helpdesk/ticket')) {
                window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
                return;
            }
        }

        // Priority 4: Default navigation based on user role
        defaultBackNavigation();
    }

    // Default back navigation based on user role and context
    function defaultBackNavigation() {
        const ticketUserId = '{{ ticket.user_id }}';
        const currentUserId = '{{ session.user_id }}';

        // For staff members with content management permissions, default to helpdesk management
        // unless they are regular users (non-staff) who created the ticket
        if (canManageContent) {
            // Staff members should go to helpdesk management by default
            window.location.href = '{{ url_for("helpdesk.get_tickets") }}';
        } else {
            // Regular users always go to their personal helpdesk
            window.location.href = '{{ url_for("helpdesk.get_user_tickets") }}';
        }
    }

    // Update back button text based on source - simplified to match smartBack logic
    function updateBackButtonText() {
        const backButtonText = document.getElementById('backButtonText');
        const urlParams = new URLSearchParams(window.location.search);
        const backUrl = urlParams.get('back');

        // Priority 1: Check explicit back URL parameter
        if (backUrl) {
            if (backUrl.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
            } else if (backUrl.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else if (backUrl.includes('/helpdesk')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            } else {
                backButtonText.textContent = 'Back';
            }
            return;
        }

        // Priority 2: Check stored source (where user came from)
        const storedSource = sessionStorage.getItem('ticketDetailOriginalSource');
        if (storedSource) {
            if (storedSource.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
            } else if (storedSource.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else if (storedSource.includes('/helpdesk') && !storedSource.includes('/helpdesk/ticket')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            } else {
                backButtonText.textContent = 'Back';
            }
            return;
        }

        // Priority 3: Check document referrer
        const referrer = document.referrer;
        if (referrer) {
            if (referrer.includes('/notifications')) {
                backButtonText.textContent = 'Back to Notifications';
            } else if (referrer.includes('/helpdesk/manage')) {
                backButtonText.textContent = 'Back to Helpdesk Management';
            } else if (referrer.includes('/helpdesk') && !referrer.includes('/helpdesk/ticket')) {
                backButtonText.textContent = 'Back to My Helpdesk Tickets';
            } else {
                backButtonText.textContent = 'Back';
            }
            return;
        }

        // Priority 4: Default based on user role
        if (canManageContent) {
            backButtonText.textContent = 'Back to Helpdesk Management';
        } else {
            backButtonText.textContent = 'Back to My Helpdesk Tickets';
        }
    }

    // Helper function to show toast notifications
    function showToast(message, type = 'success') {
        // Use the global flash message system for consistency
        if (typeof showFlashMessage === 'function') {
            showFlashMessage(message, type);
        } else {
            // Fallback to console if flash message system is not available
            console.log(`Toast: ${message} (${type})`);
        }
    }

    // Helper function to update comment section visibility
    function updateCommentSectionVisibility(updatedTicket) {
        if (!updatedTicket) return;

        const currentUserId = {{ session.user_id|default('null') }};
        const ticketUserId = updatedTicket.user_id;
        const assignedTo = updatedTicket.assigned_to;
        const ticketStatus = updatedTicket.status;

        // Check if comments should be enabled
        const isTicketClosed = ['resolved', 'approved', 'rejected'].includes(ticketStatus);
        const canComment = !isTicketClosed && (currentUserId === ticketUserId || currentUserId === assignedTo);

        const commentsSection = document.querySelector('.comments-section');
        if (commentsSection) {
            // Find existing comment form or restriction message
            const existingForm = commentsSection.querySelector('.comment-form-container');
            const existingRestriction = commentsSection.querySelector('.p-4.rounded-3.mt-3.mb-0.bg-light');

            // Remove existing form or restriction
            if (existingForm) existingForm.remove();
            if (existingRestriction) existingRestriction.remove();

            if (canComment) {
                // Add comment form
                const commentFormHtml = `
                    <div class="comment-form-container mt-4">
                        <form action="{{ url_for('helpdesk.add_reply') }}" id="post_comment" method="post" class="comment-form">
                            <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
                            <input type="hidden" name="user_id" value="${currentUserId}">
                            {% if request.args.get('back') %}
                            <input type="hidden" name="back" value="{{ request.args.get('back') }}">
                            {% endif %}
                            <input type="text" class="comment-input" id="content" placeholder="Your Comment" name="content" required>
                            <button type="submit" class="submit-btn"><i class="bi bi-send"></i></button>
                        </form>
                    </div>
                `;
                commentsSection.insertAdjacentHTML('beforeend', commentFormHtml);

                // Reattach comment form event listener
                const newCommentForm = document.getElementById('post_comment');
                if (newCommentForm) {
                    attachCommentFormListener(newCommentForm);
                }
            } else {
                // Add restriction message
                let restrictionMessage;
                if (isTicketClosed) {
                    restrictionMessage = `
                        <div class="p-4 rounded-3 mt-3 mb-0 bg-light">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Comments Disabled</h6>
                                    <p class="mb-0 small">This ticket has been ${ticketStatus} and no longer accepts new comments.</p>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    restrictionMessage = `
                        <div class="p-4 rounded-3 mt-3 mb-0 bg-light">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-info-circle fs-5 me-3 mt-1"></i>
                                <div>
                                    <h6 class="fw-bold mb-1">Comments Restricted</h6>
                                    <p class="mb-0 small">Only the ticket creator and assigned staff can add comments to this ticket.</p>
                                </div>
                            </div>
                        </div>
                    `;
                }
                commentsSection.insertAdjacentHTML('beforeend', restrictionMessage);
            }
        }
    }

    // Helper function to update ticket status display
    function updateTicketStatus(newStatus, newStatusText) {
        const statusBadge = document.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.className = `badge rounded-pill px-3 py-2 ${getStatusClass(newStatus)}`;
            statusBadge.innerHTML = getStatusIcon(newStatus) + newStatusText;
        }
    }

    // Helper function to get status badge class
    function getStatusClass(status) {
        const classes = {
            'new': 'bg-primary-subtle text-primary',
            'open': 'bg-success-subtle text-success',
            'stalled': 'bg-warning-subtle text-warning',
            'resolved': 'bg-info-subtle text-info',
            'approved': 'bg-success-subtle text-success',
            'rejected': 'bg-secondary-subtle text-secondary'
        };
        return classes[status] || 'bg-secondary-subtle text-secondary';
    }

    // Helper function to get status icon
    function getStatusIcon(status) {
        const icons = {
            'new': '<i class="bi bi-star me-1"></i>',
            'open': '<i class="bi bi-unlock me-1"></i>',
            'stalled': '<i class="bi bi-pause me-1"></i>',
            'resolved': '<i class="bi bi-check2-circle me-1"></i>',
            'approved': '<i class="bi bi-check-lg me-1"></i>',
            'rejected': '<i class="bi bi-x-circle me-1"></i>'
        };
        return icons[status] || '';
    }

    // Initialize status change preview functionality
    function initializeStatusChangePreview() {
        const statusSelect = document.querySelector('#statusSelect');
        const statusPreview = document.querySelector('#statusChangePreview');
        const newStatusBadge = document.querySelector('#newStatusBadge');

        if (!statusSelect || !statusPreview || !newStatusBadge) return;

        statusSelect.addEventListener('change', function() {
            const selectedStatus = this.value;

            if (selectedStatus) {
                // Update the new status badge
                newStatusBadge.className = `badge status-badge ${getStatusClass(selectedStatus)}`;
                newStatusBadge.innerHTML = getStatusIcon(selectedStatus) + selectedStatus.charAt(0).toUpperCase() + selectedStatus.slice(1);

                // Show the preview
                statusPreview.style.display = 'block';
            } else {
                // Hide the preview if no status selected
                statusPreview.style.display = 'none';
            }
        });
    }

    // Handle ticket actions via AJAX
    function handleTicketAction(form, action) {
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        if (submitBtn) submitBtn.disabled = true;

        return fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json();
            } else {
                // If not JSON, likely an error page - throw error with status
                throw new Error(`Server returned ${response.status}: ${response.statusText}. Expected JSON response.`);
            }
        })
        .then(data => {
            if (data.success) {
                try {
                    // Update UI based on action
                    if (action === 'change') {
                        // For change status, use simple page reload for reliability
                        // This ensures all DOM elements are properly updated
                        console.log('Status changed successfully, reloading page for complete update');
                        window.location.reload();
                        return true;
                    } else if (action === 'take' || action === 'assign' || action === 'drop') {
                        // Update status badge if provided (for take/drop actions that change status)
                        if (data.status_badge_html) {
                            const statusBadge = document.querySelector('.status-badge');
                            if (statusBadge) {
                                statusBadge.outerHTML = data.status_badge_html;
                            }
                        }

                        // Update assigned badge
                        const assignedBadgeContainer = document.querySelector('.meta-section .section-header');
                        if (assignedBadgeContainer) {
                            // Remove existing assigned badge (any badge that's not the status badge)
                            const existingAssignedBadges = assignedBadgeContainer.querySelectorAll('.badge:not(.status-badge)');
                            existingAssignedBadges.forEach(badge => badge.remove());

                            // Add new assigned badge if provided
                            if (data.assigned_badge_html) {
                                const statusBadge = assignedBadgeContainer.querySelector('.status-badge');
                                if (statusBadge) {
                                    statusBadge.insertAdjacentHTML('afterend', ' ' + data.assigned_badge_html);
                                } else {
                                    // Fallback: append to container if status badge not found
                                    assignedBadgeContainer.insertAdjacentHTML('beforeend', data.assigned_badge_html);
                                }
                            }
                        }

                        // Update appeal actions section for appeal tickets
                        if (data.appeal_actions_html) {
                            const appealActionsContainer = document.querySelector('.appeal-actions-section');
                            if (appealActionsContainer) {
                                appealActionsContainer.innerHTML = data.appeal_actions_html;
                                // Reattach event listeners to new appeal buttons if any
                                attachAppealActionListeners();
                            }
                        }

                        // Update entire status body section
                        if (data.status_body_html) {
                            const statusBodyContainer = document.querySelector('.status-body');
                            if (statusBodyContainer) {
                                statusBodyContainer.outerHTML = data.status_body_html;
                                // Reattach event listeners to new appeal buttons if any
                                attachAppealActionListeners();
                            }
                        }

                        // Update comment section visibility based on new assignment
                        updateCommentSectionVisibility(data.updated_ticket);
                    }

                    // Update action buttons if needed
                    if (data.actions_html) {
                        // Target the specific dropdown menu in the ticket detail panel
                        const actionsContainer = document.querySelector('.ticket-detail-panel .dropdown-menu');
                        if (actionsContainer) {
                            actionsContainer.innerHTML = data.actions_html;
                            // Reattach event listeners to new action buttons
                            attachActionListeners();
                            attachAppealActionListeners();
                        }
                    }

                    // Update assignment form template if provided
                    if (data.assign_form_html) {
                        const assignFormTemplate = document.getElementById('assignTicketFormTemplate');
                        if (assignFormTemplate) {
                            assignFormTemplate.innerHTML = data.assign_form_html;
                        }
                    }

                    // Update take form template if provided
                    if (data.take_form_html) {
                        const takeFormTemplate = document.getElementById('takeTicketFormTemplate');
                        if (takeFormTemplate) {
                            takeFormTemplate.innerHTML = data.take_form_html;
                        }
                    }

                    // Show success message
                    showToast(data.message || 'Action completed successfully');
                    return true;
                } catch (uiError) {
                    console.error('Error updating UI:', uiError);
                    // Still show success message even if UI update fails
                    showToast(data.message || 'Action completed successfully');
                    return true;
                }
            } else {
                throw new Error(data.message || 'Error processing request');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast(error.message || 'Error processing request', 'danger');
            return false;
        })
        .finally(() => {
            if (submitBtn) submitBtn.disabled = false;
        });
    }

    // Note: Appeal actions now use normal form submission (non-AJAX) for better reliability

    // Helper function to attach comment form event listener
    function attachCommentFormListener(commentForm) {
        if (!commentForm) return;

        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);
            const commentInput = this.querySelector('#content');
            const submitBtn = this.querySelector('button[type="submit"]');

            // Disable form while submitting
            commentInput.disabled = true;
            submitBtn.disabled = true;

            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    try {
                        // Add comment to the list
                        const commentsList = document.querySelector('.comments-list');
                        if (commentsList) {
                            const emptyState = commentsList.querySelector('.empty-state');
                            if (emptyState) {
                                emptyState.remove();
                            }

                            if (data.reply) {
                                const commentElement = document.createElement('div');
                                commentElement.className = 'comment-item';
                                commentElement.innerHTML = `
                                    <div class="comment-avatar">
                                        <img src="${data.reply.profile_image ? '/static/uploads/profile_images/' + data.reply.profile_image : '/static/uploads/profile_images/profile_placeholder.png'}"
                                             alt="User Avatar"
                                             onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                                    </div>
                                    <div class="comment-content">
                                        <div class="comment-header">
                                            <span class="comment-author">${data.reply.username || 'Unknown'}</span>
                                            <span class="comment-time"><i class="bi bi-clock me-1"></i>${data.reply.created_at || 'Just now'}</span>
                                        </div>
                                        <div class="comment-text">${data.reply.content || ''}</div>
                                    </div>
                                `;
                                commentsList.appendChild(commentElement);
                            }

                            // Update comment count
                            const commentCount = document.querySelector('.comment-count');
                            if (commentCount) {
                                const currentCount = parseInt(commentCount.textContent) || 0;
                                commentCount.textContent = currentCount + 1;
                            }
                        }

                        // Clear the input
                        this.reset();
                        showToast('Comment added successfully');
                    } catch (uiError) {
                        console.error('Error updating comment UI:', uiError);
                        // Still show success message and clear form
                        this.reset();
                        showToast('Comment added successfully');
                    }
                } else {
                    throw new Error(data.message || 'Error adding comment');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast(error.message || 'Error adding comment', 'danger');
            })
            .finally(() => {
                // Re-enable form
                commentInput.disabled = false;
                submitBtn.disabled = false;
            });
        });
    }

    // Helper function to attach event listeners to appeal action buttons
    function attachAppealActionListeners() {
        document.querySelectorAll('.appeal-action-btn').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                const appealId = this.getAttribute('data-appeal-id');

                // Safety check: Only allow appeal actions on appeal tickets assigned to current user
                const ticketRequestType = '{{ ticket.request_type }}';
                const ticketAppealType = '{{ ticket.appeal_type }}';
                const ticketStatus = '{{ ticket.status }}';
                const ticketAssignedTo = {{ ticket.assigned_to|default('null') }};
                const currentUserId = {{ session.user_id|default('null') }};

                if (ticketRequestType !== 'appeal' || !ticketAppealType) {
                    console.error('Appeal action attempted on non-appeal ticket:', {
                        requestType: ticketRequestType,
                        appealType: ticketAppealType
                    });
                    showToast('Error: This is not an appeal ticket', 'danger');
                    return;
                }

                if (!['new', 'open'].includes(ticketStatus) || ticketAssignedTo !== currentUserId) {
                    console.error('Appeal action attempted on ticket not assigned to current user:', {
                        status: ticketStatus,
                        assignedTo: ticketAssignedTo,
                        currentUser: currentUserId
                    });
                    showToast('Error: You are not assigned to this appeal', 'danger');
                    return;
                }

                let formHtml, title, actionText, actionClass;

                if (action === 'approve') {
                    const template = document.getElementById('approveAppealFormTemplate');
                    if (!template) {
                        console.error('Approve appeal form template not found');
                        showToast('Error: Appeal form template not found', 'danger');
                        return;
                    }
                    formHtml = template.innerHTML;
                    title = 'Approve Appeal';
                    actionText = 'Approve';
                    actionClass = 'btn-success';
                } else if (action === 'reject') {
                    const template = document.getElementById('rejectAppealFormTemplate');
                    if (!template) {
                        console.error('Reject appeal form template not found');
                        showToast('Error: Appeal form template not found', 'danger');
                        return;
                    }
                    formHtml = template.innerHTML;
                    title = 'Reject Appeal';
                    actionText = 'Reject';
                    actionClass = 'btn-danger';
                }

                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function() {
                        const form = document.querySelector('#commonModal form');
                        if (form) {
                            // Fix form action URL - set it correctly since DOMParser corrupts it
                            if (appealProcessUrl && appealProcessUrl !== 'undefined' && appealProcessUrl !== null) {
                                form.action = appealProcessUrl;
                                console.log('Setting form action to:', appealProcessUrl);
                            } else {
                                console.error('Invalid appealProcessUrl:', appealProcessUrl);
                                console.error('This ticket may not be an appeal ticket');
                                showToast('Error: This is not a valid appeal ticket', 'danger');
                                return false;
                            }

                            if (action === 'reject') {
                                const responseField = form.querySelector('textarea[name="response"]');
                                if (!responseField.value.trim()) {
                                    responseField.focus();
                                    responseField.classList.add('is-invalid');
                                    return false;
                                }
                            }

                            // Debug: Log the form action URL
                            console.log('Appeal form action URL (corrected):', form.action);

                            // Submit form normally (non-AJAX) for appeals - more reliable
                            console.log('Submitting appeal form normally (non-AJAX)');
                            form.submit();
                            return false;
                        }
                    }
                });
            });
        });
    }

    // Helper function to attach event listeners to action buttons
    function attachActionListeners() {
        document.querySelectorAll('.modal-trigger').forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                const action = this.getAttribute('data-action');
                let formHtml, title, actionText, actionClass;

                // Get current ticket state from DOM to determine correct messages
                const currentAssignedBadge = document.querySelector('.meta-section .section-header .badge:not(.status-badge)');
                const isCurrentlyAssigned = currentAssignedBadge !== null;

                if (action === 'assign') {
                    formHtml = document.getElementById('assignTicketFormTemplate').innerHTML;
                    title = isCurrentlyAssigned ? 'Reassign Ticket' : 'Assign Ticket';
                    actionText = 'Assign';
                    actionClass = 'btn-primary';
                } else if (action === 'change') {
                    formHtml = document.getElementById('changeStatusFormTemplate').innerHTML;
                    title = 'Change Status';
                    actionText = 'Update';
                    actionClass = 'btn-primary';
                } else if (action === 'take') {
                    formHtml = document.getElementById('takeTicketFormTemplate').innerHTML;
                    title = isCurrentlyAssigned ? 'Take Over Ticket' : 'Take Ticket';
                    actionText = isCurrentlyAssigned ? 'Take Over' : 'Take';
                    actionClass = isCurrentlyAssigned ? 'btn-warning' : 'btn-success';
                } else if (action === 'drop') {
                    formHtml = document.getElementById('dropTicketFormTemplate').innerHTML;
                    title = 'Drop Ticket';
                    actionText = 'Drop';
                    actionClass = 'btn-warning';
                }

                showModal(title, formHtml, {
                    actionText: actionText,
                    actionClass: actionClass,
                    onAction: function() {
                        const form = document.querySelector('#commonModal form');
                        if (form) {
                            // Validate form if needed
                            if (action === 'change') {
                                const statusSelect = form.querySelector('select[name="status"]');
                                if (!statusSelect.value) {
                                    statusSelect.focus();
                                    statusSelect.classList.add('is-invalid');
                                    return false;
                                }
                            } else if (action === 'assign') {
                                const staffSelect = form.querySelector('select[name="staff_id"]');
                                if (!staffSelect.value) {
                                    staffSelect.focus();
                                    staffSelect.classList.add('is-invalid');
                                    showToast('Please select a staff member to assign the ticket to', 'danger');
                                    return false;
                                }
                            }

                            // Handle action via AJAX
                            handleTicketAction(form, action).then(success => {
                                if (success) {
                                    const modal = bootstrap.Modal.getInstance(document.querySelector('#commonModal'));
                                    modal.hide();
                                }
                            });
                            return false; // Prevent modal from closing automatically
                        }
                    },
                    onShow: function() {
                        if (action === 'change') {
                            initializeStatusChangePreview();
                        }
                    }
                });
            });
        });
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Debug: Log ticket information
        console.log('Ticket Debug Info:', {
            ticketId: '{{ ticket.id }}',
            requestType: '{{ ticket.request_type }}',
            appealType: '{{ ticket.appeal_type }}',
            status: '{{ ticket.status }}',
            subject: '{{ ticket.subject }}',
            appealProcessUrl: appealProcessUrl
        });

        // Store original source on first load
        if (!sessionStorage.getItem('ticketDetailOriginalSource')) {
            const referrer = document.referrer;
            const urlParams = new URLSearchParams(window.location.search);
            const backParam = urlParams.get('back');

            if (backParam) {
                sessionStorage.setItem('ticketDetailOriginalSource', backParam);
            } else if (referrer && !referrer.includes(window.location.pathname)) {
                sessionStorage.setItem('ticketDetailOriginalSource', referrer);
            }
        }

        // Update back button text
        updateBackButtonText();

        // Handle comment form submission via AJAX
        const commentForm = document.getElementById('post_comment');
        if (commentForm) {
            commentForm.addEventListener('submit', function(e) {
                e.preventDefault();
                const formData = new FormData(this);
                const commentInput = this.querySelector('#content');
                const submitBtn = this.querySelector('button[type="submit"]');
                
                // Disable form while submitting
                commentInput.disabled = true;
                submitBtn.disabled = true;

                fetch(this.action, {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        try {
                            // Add comment to the list
                            const commentsList = document.querySelector('.comments-list');
                            if (commentsList) {
                                const emptyState = commentsList.querySelector('.empty-state');
                                if (emptyState) {
                                    emptyState.remove();
                                }

                                if (data.reply) {
                                    const commentElement = document.createElement('div');
                                    commentElement.className = 'comment-item';
                                    commentElement.innerHTML = `
                                        <div class="comment-avatar">
                                            <img src="${data.reply.profile_image ? '/static/uploads/profile_images/' + data.reply.profile_image : '/static/uploads/profile_images/profile_placeholder.png'}"
                                                 alt="User Avatar"
                                                 onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png'">
                                        </div>
                                        <div class="comment-content">
                                            <div class="comment-header">
                                                <span class="comment-author">${data.reply.username || 'Unknown'}</span>
                                                <span class="comment-time"><i class="bi bi-clock me-1"></i>${data.reply.created_at || 'Just now'}</span>
                                            </div>
                                            <div class="comment-text">${data.reply.content || ''}</div>
                                        </div>
                                    `;
                                    commentsList.appendChild(commentElement);
                                }

                                // Update comment count
                                const commentCount = document.querySelector('.comment-count');
                                if (commentCount) {
                                    const currentCount = parseInt(commentCount.textContent) || 0;
                                    commentCount.textContent = currentCount + 1;
                                }
                            }

                            // Clear the input
                            this.reset();
                            showToast('Comment added successfully');
                        } catch (uiError) {
                            console.error('Error updating comment UI:', uiError);
                            // Still show success message and clear form
                            this.reset();
                            showToast('Comment added successfully');
                        }
                    } else {
                        throw new Error(data.message || 'Error adding comment');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast(error.message || 'Error adding comment', 'danger');
                })
                .finally(() => {
                    // Re-enable form
                    commentInput.disabled = false;
                    submitBtn.disabled = false;
                });
            });
        }

        // Handle ticket actions (take, drop, assign, change status)
        attachActionListeners();

        // Handle appeal actions
        attachAppealActionListeners();
    });
</script>
{% endblock %}